/* Copyright 2025 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.controller;

import static com.fitb.digital.bff.movemoney.featureflags.FeatureFlagNames.CORE_TRANSFERS_TDS_ENABLED;
import static com.fitb.digital.bff.movemoney.featureflags.FeatureFlagNames.ORCHESTRATOR_ENABLED;
import static com.fitb.digital.bff.movemoney.utils.TestUtils.mockFromFile;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fitb.digital.bff.movemoney.client.CesClient;
import com.fitb.digital.bff.movemoney.client.CoreTransfersClient;
import com.fitb.digital.bff.movemoney.featureflags.ActivityFeatureFlagChecker;
import com.fitb.digital.bff.movemoney.featureflags.FeatureFlagVariants;
import com.fitb.digital.bff.movemoney.model.bff.CoreTransfers.responses.TDSCoreTransferActivity;
import com.fitb.digital.bff.movemoney.model.bff.CoreTransfers.responses.TDSCoreTransferActivityResponse;
import com.fitb.digital.bff.movemoney.model.bff.CoreTransfers.responses.TDSCoreTransferResponse;
import com.fitb.digital.bff.movemoney.model.client.activity.responses.ClientActivityResponse;
import com.fitb.digital.lib.featureflag.service.FeatureFlagService;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Arrays;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

@SpringBootTest
@AutoConfigureMockMvc
public class CoreTransfersActivityIntegrationTest {

  @Autowired private MockMvc mockMvc;

  @MockBean FeatureFlagService featureFlagService;
  @MockBean CoreTransfersClient coreTransfersClient;
  @MockBean CesClient cesClient;
  @MockBean ActivityFeatureFlagChecker activityFeatureFlagChecker;

  static final String BEARER_TOKEN =
      "Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6IlFac0xydXlkWEdPQ01qaGk4OXdldGlKa25aVSIsInBpLmF0bSI6Im51eWoifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NsXi0ciImrNR9gsrfQNAO34RKbqMcCPIZjjt8KU3i0xLe86vM_Q4FP8JF8lLdgCJAOUc3lZOTMxD72MPh7TiDJWs7uDZBE7TROIUfSMen4l8mGFRsSF9n4xqThTt93CkdXEoBFh4AuV8SXFYc8vTrDK0cVRWgos918AbpknfYwWxNZZnFzaXsbL_F7wXxx1cQc_GjYb1_ETVmOwKMkIwI_wd-xDbrOC2cOtX0mtxX3Cp3xsYf22324nl3kp8-ObAfcQ4XwzptQ9ReW3XHl3bkbDjPw7ZtA47SNflCgndNXinzEBvL71pwCAlDTdEzcnYHbLUwXSxvaJW-Meri4maSA";

  @BeforeEach
  void setUp() {
    reset(featureFlagService, coreTransfersClient, cesClient, activityFeatureFlagChecker);

    // Setup default feature flag variants to avoid feature flag exceptions
    when(featureFlagService.getFeatureVariant(anyString(), anyString()))
        .thenReturn(FeatureFlagVariants.ENABLED);
  }

  // ===== ADDITIONAL COMPREHENSIVE TESTS =====

  @Test
  @DisplayName("Create transfer with zero amount")
  void createCoreTransfer_ZeroAmount_HandlesCorrectly() throws Exception {
    // Setup feature flags
    when(featureFlagService.isFeatureEnabled(ORCHESTRATOR_ENABLED)).thenReturn(false);
    when(featureFlagService.isFeatureEnabled(CORE_TRANSFERS_TDS_ENABLED)).thenReturn(true);

    // Mock successful core transfer creation
    TDSCoreTransferResponse transferResponse = createSuccessfulTransferResponse();
    when(coreTransfersClient.tdsCoreTransfer(any())).thenReturn(transferResponse);

    var transferContent = createTransferRequest("DDA", "SAV", new BigDecimal("0.00"));

    this.mockMvc
        .perform(
            post("/activity")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header("Authorization", BEARER_TOKEN)
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .content(transferContent)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.activity.amount").value(0.00));
  }

  @Test
  @DisplayName("Create transfer with large amount")
  void createCoreTransfer_LargeAmount_HandlesCorrectly() throws Exception {
    // Setup feature flags
    when(featureFlagService.isFeatureEnabled(ORCHESTRATOR_ENABLED)).thenReturn(false);
    when(featureFlagService.isFeatureEnabled(CORE_TRANSFERS_TDS_ENABLED)).thenReturn(true);

    // Mock successful core transfer creation
    TDSCoreTransferResponse transferResponse = createSuccessfulTransferResponse();
    when(coreTransfersClient.tdsCoreTransfer(any())).thenReturn(transferResponse);

    var transferContent = createTransferRequest("DDA", "SAV", new BigDecimal("999999.99"));

    this.mockMvc
        .perform(
            post("/activity")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header("Authorization", BEARER_TOKEN)
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .content(transferContent)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.activity.amount").value(999999.99));
  }

  @Test
  @DisplayName("Create multiple transfers and verify all succeed")
  void createMultipleTransfers_AllSucceed() throws Exception {
    // Setup feature flags
    when(featureFlagService.isFeatureEnabled(ORCHESTRATOR_ENABLED)).thenReturn(false);
    when(featureFlagService.isFeatureEnabled(CORE_TRANSFERS_TDS_ENABLED)).thenReturn(true);

    // Mock successful core transfer creation
    TDSCoreTransferResponse transferResponse1 = createSuccessfulTransferResponse();
    transferResponse1.setReferenceId("REF-001");
    TDSCoreTransferResponse transferResponse2 = createSuccessfulTransferResponse();
    transferResponse2.setReferenceId("REF-002");

    when(coreTransfersClient.tdsCoreTransfer(any()))
        .thenReturn(transferResponse1)
        .thenReturn(transferResponse2);

    // Create first transfer
    var transferContent1 = createTransferRequest("DDA", "SAV", new BigDecimal("100.00"));
    this.mockMvc
        .perform(
            post("/activity")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header("Authorization", BEARER_TOKEN)
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .content(transferContent1)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.status").value("SUCCESS"));

    // Create second transfer
    var transferContent2 = createTransferRequest("SAV", "DDA", new BigDecimal("50.00"));
    this.mockMvc
        .perform(
            post("/activity")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header("Authorization", BEARER_TOKEN)
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .content(transferContent2)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.status").value("SUCCESS"));

    // Verify both transfers were created
    verify(coreTransfersClient, times(2)).tdsCoreTransfer(any());
  }

  @Test
  @DisplayName("Get activity with zero limits")
  void getActivity_ZeroLimits_ReturnsEmptyArrays() throws Exception {
    // Setup feature flags
    when(featureFlagService.isFeatureEnabled(ORCHESTRATOR_ENABLED)).thenReturn(false);
    when(featureFlagService.isFeatureEnabled(CORE_TRANSFERS_TDS_ENABLED)).thenReturn(true);

    // Mock responses
    ClientActivityResponse cesResponse =
        mockFromFile("mock_ces_get_activity_response.json", ClientActivityResponse.class);
    when(cesClient.getTransferAndPayActivity()).thenReturn(cesResponse);

    TDSCoreTransferActivityResponse coreTransfersResponse = createCoreTransfersActivityResponse();
    when(coreTransfersClient.getTransferActivity()).thenReturn(coreTransfersResponse);

    this.mockMvc
        .perform(
            get("/v2/activity")
                .param("recentLimit", "0")
                .param("upcomingLimit", "0")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header("Authorization", BEARER_TOKEN)
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.status").value("SUCCESS"))
        .andExpect(jsonPath("$.recentActivities").isArray())
        .andExpect(jsonPath("$.recentActivities.length()").value(0))
        .andExpect(jsonPath("$.upcomingActivities").isArray())
        .andExpect(jsonPath("$.upcomingActivities.length()").value(0));
  }

  @Test
  @DisplayName("Create immediate core transfer - Success scenario")
  void createImmediateCoreTransfer_Success() throws Exception {
    // Setup feature flags
    when(featureFlagService.isFeatureEnabled(ORCHESTRATOR_ENABLED)).thenReturn(false);
    when(featureFlagService.isFeatureEnabled(CORE_TRANSFERS_TDS_ENABLED)).thenReturn(true);

    // Mock successful core transfer creation
    TDSCoreTransferResponse transferResponse = createSuccessfulTransferResponse();
    when(coreTransfersClient.tdsCoreTransfer(any())).thenReturn(transferResponse);

    // Create immediate transfer
    var transferContent = createImmediateTransferRequest();

    this.mockMvc
        .perform(
            post("/activity")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header("Authorization", BEARER_TOKEN)
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .content(transferContent)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.status").value("SUCCESS"))
        .andExpect(jsonPath("$.activity.activityType").value("INTERNAL_TRANSFER"))
        .andExpect(jsonPath("$.activity.displayStatus").value("Processed"))
        .andExpect(jsonPath("$.activity.amount").value(100.50));

    // Verify the core transfer request was made
    verify(coreTransfersClient, times(1)).tdsCoreTransfer(any());
  }

  @Test
  @DisplayName("Create core transfer with insufficient funds error")
  void createCoreTransfer_InsufficientFunds_ReturnsError() throws Exception {
    // Setup feature flags
    when(featureFlagService.isFeatureEnabled(ORCHESTRATOR_ENABLED)).thenReturn(false);
    when(featureFlagService.isFeatureEnabled(CORE_TRANSFERS_TDS_ENABLED)).thenReturn(true);

    // Mock error response from core transfers
    TDSCoreTransferResponse errorResponse =
        createErrorTransferResponse("INSUFFICIENT_FUNDS", "Insufficient funds in source account");
    when(coreTransfersClient.tdsCoreTransfer(any())).thenReturn(errorResponse);

    var transferContent = createImmediateTransferRequest();

    this.mockMvc
        .perform(
            post("/activity")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header("Authorization", BEARER_TOKEN)
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .content(transferContent)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andExpect(jsonPath("$.status").value("ERROR"));
  }

  @Test
  @DisplayName("Create core transfer with invalid account error")
  void createCoreTransfer_InvalidAccount_ReturnsError() throws Exception {
    // Setup feature flags
    when(featureFlagService.isFeatureEnabled(ORCHESTRATOR_ENABLED)).thenReturn(false);
    when(featureFlagService.isFeatureEnabled(CORE_TRANSFERS_TDS_ENABLED)).thenReturn(true);

    // Mock error response from core transfers
    TDSCoreTransferResponse errorResponse =
        createErrorTransferResponse("INVALID_ACCOUNT", "Invalid account specified");
    when(coreTransfersClient.tdsCoreTransfer(any())).thenReturn(errorResponse);

    var transferContent = createImmediateTransferRequest();

    this.mockMvc
        .perform(
            post("/activity")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header("Authorization", BEARER_TOKEN)
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .content(transferContent)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andExpect(jsonPath("$.status").value("ERROR"));
  }

  @Test
  @DisplayName("Create core transfer with different account types")
  void createCoreTransfer_DifferentAccountTypes_Success() throws Exception {
    // Setup feature flags
    when(featureFlagService.isFeatureEnabled(ORCHESTRATOR_ENABLED)).thenReturn(false);
    when(featureFlagService.isFeatureEnabled(CORE_TRANSFERS_TDS_ENABLED)).thenReturn(true);

    // Mock successful core transfer creation
    TDSCoreTransferResponse transferResponse = createSuccessfulTransferResponse();
    when(coreTransfersClient.tdsCoreTransfer(any())).thenReturn(transferResponse);

    // Create transfer with different account types (DDA to DDA)
    var transferContent = createTransferRequest("DDA", "DDA", new BigDecimal("250.75"));

    this.mockMvc
        .perform(
            post("/activity")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header("Authorization", BEARER_TOKEN)
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .content(transferContent)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.status").value("SUCCESS"))
        .andExpect(jsonPath("$.activity.amount").value(250.75));
  }

  @Test
  @DisplayName("Create core transfer when feature flag disabled - Falls back to CES")
  void createCoreTransfer_FeatureFlagDisabled_FallsBackToCes() throws Exception {
    // Setup feature flags - Core Transfers disabled
    when(featureFlagService.isFeatureEnabled(ORCHESTRATOR_ENABLED)).thenReturn(false);
    when(featureFlagService.isFeatureEnabled(CORE_TRANSFERS_TDS_ENABLED)).thenReturn(false);

    var transferContent = createImmediateTransferRequest();

    this.mockMvc
        .perform(
            post("/activity")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header("Authorization", BEARER_TOKEN)
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .content(transferContent)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk());

    // Verify Core Transfers client was never called
    verify(coreTransfersClient, never()).tdsCoreTransfer(any());
  }

  // ===== TRANSFER ACTIVITY RETRIEVAL TESTS =====

  @Test
  @DisplayName("Get activity - Both CES and Core Transfers succeed")
  void getActivity_BothServicesSucceed_ReturnsSuccess() throws Exception {
    // Setup feature flags
    when(featureFlagService.isFeatureEnabled(ORCHESTRATOR_ENABLED)).thenReturn(false);
    when(featureFlagService.isFeatureEnabled(CORE_TRANSFERS_TDS_ENABLED)).thenReturn(true);

    // Mock successful responses from both services
    ClientActivityResponse cesResponse =
        mockFromFile("mock_ces_get_activity_response.json", ClientActivityResponse.class);
    when(cesClient.getTransferAndPayActivity()).thenReturn(cesResponse);

    TDSCoreTransferActivityResponse coreTransfersResponse = createCoreTransfersActivityResponse();
    when(coreTransfersClient.getTransferActivity()).thenReturn(coreTransfersResponse);

    this.mockMvc
        .perform(
            get("/v2/activity")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header("Authorization", BEARER_TOKEN)
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.status").value("SUCCESS"))
        .andExpect(jsonPath("$.recentActivities").isArray())
        .andExpect(jsonPath("$.upcomingActivities").isArray())
        .andExpect(jsonPath("$.recentActivities[?(@.amount == 100.50)]").exists());
  }

  @Test
  @DisplayName("Get activity - CES fails, Core Transfers succeeds")
  void getActivity_CesFailsCoreTransfersSucceeds_ReturnsPartialSuccess() throws Exception {
    // Setup feature flags
    when(featureFlagService.isFeatureEnabled(ORCHESTRATOR_ENABLED)).thenReturn(false);
    when(featureFlagService.isFeatureEnabled(CORE_TRANSFERS_TDS_ENABLED)).thenReturn(true);

    // Mock CES failure
    when(cesClient.getTransferAndPayActivity())
        .thenThrow(new RuntimeException("CES service unavailable"));

    // Mock successful Core Transfers response
    TDSCoreTransferActivityResponse coreTransfersResponse = createCoreTransfersActivityResponse();
    when(coreTransfersClient.getTransferActivity()).thenReturn(coreTransfersResponse);

    this.mockMvc
        .perform(
            get("/v2/activity")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header("Authorization", BEARER_TOKEN)
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.status").value("PARTIAL_SUCCESS"))
        .andExpect(jsonPath("$.retrievalErrors").isArray())
        .andExpect(jsonPath("$.retrievalErrors[0]").value("UNABLE_TO_GET_ACTIVITY"))
        .andExpect(jsonPath("$.recentActivities").isArray())
        .andExpect(jsonPath("$.recentActivities[?(@.amount == 100.50)]").exists());
  }

  @Test
  @DisplayName("Get activity - Core Transfers fails, CES succeeds")
  void getActivity_CoreTransfersFailsCesSucceeds_ReturnsPartialSuccess() throws Exception {
    // Setup feature flags
    when(featureFlagService.isFeatureEnabled(ORCHESTRATOR_ENABLED)).thenReturn(false);
    when(featureFlagService.isFeatureEnabled(CORE_TRANSFERS_TDS_ENABLED)).thenReturn(true);

    // Mock successful CES response
    ClientActivityResponse cesResponse =
        mockFromFile("mock_ces_get_activity_response.json", ClientActivityResponse.class);
    when(cesClient.getTransferAndPayActivity()).thenReturn(cesResponse);

    // Mock Core Transfers failure
    when(coreTransfersClient.getTransferActivity())
        .thenThrow(new RuntimeException("Core Transfers service unavailable"));

    this.mockMvc
        .perform(
            get("/v2/activity")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header("Authorization", BEARER_TOKEN)
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.status").value("PARTIAL_SUCCESS"))
        .andExpect(jsonPath("$.retrievalErrors").isArray())
        .andExpect(jsonPath("$.retrievalErrors[0]").value("RETRIEVAL_ERROR_CORE_TRANSFERS"))
        .andExpect(jsonPath("$.recentActivities").isArray())
        .andExpect(jsonPath("$.upcomingActivities").isArray());
  }

  @Test
  @DisplayName("Get activity - Both services fail")
  void getActivity_BothServicesFailure_ThrowsServiceUnavailable() throws Exception {
    // Setup feature flags
    when(featureFlagService.isFeatureEnabled(ORCHESTRATOR_ENABLED)).thenReturn(false);
    when(featureFlagService.isFeatureEnabled(CORE_TRANSFERS_TDS_ENABLED)).thenReturn(true);

    // Mock both services failing
    when(cesClient.getTransferAndPayActivity())
        .thenThrow(new RuntimeException("CES service unavailable"));
    when(coreTransfersClient.getTransferActivity())
        .thenThrow(new RuntimeException("Core Transfers service unavailable"));

    this.mockMvc
        .perform(
            get("/v2/activity")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header("Authorization", BEARER_TOKEN)
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isServiceUnavailable())
        .andExpect(jsonPath("$.status").value("SERVICE_UNAVAILABLE"));
  }

  @Test
  @DisplayName("Get activity - Core Transfers disabled, only CES used")
  void getActivity_CoreTransfersDisabled_OnlyUsesCes() throws Exception {
    // Setup feature flags
    when(featureFlagService.isFeatureEnabled(ORCHESTRATOR_ENABLED)).thenReturn(false);
    when(featureFlagService.isFeatureEnabled(CORE_TRANSFERS_TDS_ENABLED)).thenReturn(false);

    // Mock successful CES response
    ClientActivityResponse cesResponse =
        mockFromFile("mock_ces_get_activity_response.json", ClientActivityResponse.class);
    when(cesClient.getTransferAndPayActivity()).thenReturn(cesResponse);

    this.mockMvc
        .perform(
            get("/v2/activity")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header("Authorization", BEARER_TOKEN)
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.status").value("SUCCESS"))
        .andExpect(jsonPath("$.recentActivities").isArray())
        .andExpect(jsonPath("$.upcomingActivities").isArray());

    // Verify Core Transfers client was never called
    verify(coreTransfersClient, never()).getTransferActivity();
  }

  @Test
  @DisplayName("Get activity with limits - Applies limits correctly")
  void getActivity_WithLimits_AppliesLimitsCorrectly() throws Exception {
    // Setup feature flags
    when(featureFlagService.isFeatureEnabled(ORCHESTRATOR_ENABLED)).thenReturn(false);
    when(featureFlagService.isFeatureEnabled(CORE_TRANSFERS_TDS_ENABLED)).thenReturn(true);

    // Mock responses with multiple activities
    ClientActivityResponse cesResponse =
        mockFromFile("mock_ces_get_activity_response.json", ClientActivityResponse.class);
    when(cesClient.getTransferAndPayActivity()).thenReturn(cesResponse);

    TDSCoreTransferActivityResponse coreTransfersResponse = createMultipleCoreTransfersActivities();
    when(coreTransfersClient.getTransferActivity()).thenReturn(coreTransfersResponse);

    this.mockMvc
        .perform(
            get("/v2/activity")
                .param("recentLimit", "2")
                .param("upcomingLimit", "3")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header("Authorization", BEARER_TOKEN)
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.status").value("SUCCESS"))
        .andExpect(jsonPath("$.recentActivities").isArray())
        .andExpect(jsonPath("$.recentActivities.length()").value(2))
        .andExpect(jsonPath("$.upcomingActivities").isArray())
        .andExpect(jsonPath("$.upcomingActivities.length()").value(3))
        .andExpect(jsonPath("$.recentTruncated").value(true))
        .andExpect(jsonPath("$.upcomingTruncated").value(true));
  }

  @Test
  @DisplayName("Get activity with different transfer statuses")
  void getActivity_DifferentTransferStatuses_MapsCorrectly() throws Exception {
    // Setup feature flags
    when(featureFlagService.isFeatureEnabled(ORCHESTRATOR_ENABLED)).thenReturn(false);
    when(featureFlagService.isFeatureEnabled(CORE_TRANSFERS_TDS_ENABLED)).thenReturn(true);

    // Mock CES response
    ClientActivityResponse cesResponse =
        mockFromFile("mock_ces_get_activity_response.json", ClientActivityResponse.class);
    when(cesClient.getTransferAndPayActivity()).thenReturn(cesResponse);

    // Mock Core Transfers response with different statuses
    TDSCoreTransferActivityResponse coreTransfersResponse = createActivitiesWithDifferentStatuses();
    when(coreTransfersClient.getTransferActivity()).thenReturn(coreTransfersResponse);

    this.mockMvc
        .perform(
            get("/v2/activity")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header("Authorization", BEARER_TOKEN)
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.status").value("SUCCESS"))
        .andExpect(jsonPath("$.recentActivities[?(@.displayStatus == 'Completed')]").exists())
        .andExpect(jsonPath("$.recentActivities[?(@.displayStatus == 'Unsuccessful')]").exists())
        .andExpect(jsonPath("$.upcomingActivities[?(@.displayStatus == 'Scheduled')]").exists());
  }

  @Test
  @DisplayName("Get activity - Empty Core Transfers response")
  void getActivity_EmptyCoreTransfersResponse_HandlesGracefully() throws Exception {
    // Setup feature flags
    when(featureFlagService.isFeatureEnabled(ORCHESTRATOR_ENABLED)).thenReturn(false);
    when(featureFlagService.isFeatureEnabled(CORE_TRANSFERS_TDS_ENABLED)).thenReturn(true);

    // Mock CES response
    ClientActivityResponse cesResponse =
        mockFromFile("mock_ces_get_activity_response.json", ClientActivityResponse.class);
    when(cesClient.getTransferAndPayActivity()).thenReturn(cesResponse);

    // Mock empty Core Transfers response
    TDSCoreTransferActivityResponse emptyResponse = new TDSCoreTransferActivityResponse();
    when(coreTransfersClient.getTransferActivity()).thenReturn(emptyResponse);

    this.mockMvc
        .perform(
            get("/v2/activity")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header("Authorization", BEARER_TOKEN)
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.status").value("SUCCESS"))
        .andExpect(jsonPath("$.recentActivities").isArray())
        .andExpect(jsonPath("$.upcomingActivities").isArray());
  }

  @Test
  @DisplayName("Get activity - Null Core Transfers response")
  void getActivity_NullCoreTransfersResponse_HandlesGracefully() throws Exception {
    // Setup feature flags
    when(featureFlagService.isFeatureEnabled(ORCHESTRATOR_ENABLED)).thenReturn(false);
    when(featureFlagService.isFeatureEnabled(CORE_TRANSFERS_TDS_ENABLED)).thenReturn(true);

    // Mock CES response
    ClientActivityResponse cesResponse =
        mockFromFile("mock_ces_get_activity_response.json", ClientActivityResponse.class);
    when(cesClient.getTransferAndPayActivity()).thenReturn(cesResponse);

    // Mock null Core Transfers response
    when(coreTransfersClient.getTransferActivity()).thenReturn(null);

    this.mockMvc
        .perform(
            get("/v2/activity")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header("Authorization", BEARER_TOKEN)
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.status").value("SUCCESS"))
        .andExpect(jsonPath("$.recentActivities").isArray())
        .andExpect(jsonPath("$.upcomingActivities").isArray());
  }

  // ===== END-TO-END INTEGRATION TESTS =====

  @Test
  @DisplayName("Create immediate transfer and verify in activity retrieval")
  void createImmediateCoreTransferAndGetActivity_Success() throws Exception {
    // Setup feature flags
    when(featureFlagService.isFeatureEnabled(ORCHESTRATOR_ENABLED)).thenReturn(false);
    when(featureFlagService.isFeatureEnabled(CORE_TRANSFERS_TDS_ENABLED)).thenReturn(true);

    // Mock successful core transfer creation
    TDSCoreTransferResponse transferResponse = createSuccessfulTransferResponse();
    when(coreTransfersClient.tdsCoreTransfer(any())).thenReturn(transferResponse);

    // Create immediate transfer
    var transferContent = createImmediateTransferRequest();

    this.mockMvc
        .perform(
            post("/activity")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header("Authorization", BEARER_TOKEN)
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .content(transferContent)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.status").value("SUCCESS"))
        .andExpect(jsonPath("$.activity.activityType").value("INTERNAL_TRANSFER"))
        .andExpect(jsonPath("$.activity.displayStatus").value("Processed"))
        .andExpect(jsonPath("$.activity.amount").value(100.50));

    // Mock activity retrieval with both CES and Core Transfers data
    ClientActivityResponse cesResponse =
        mockFromFile("mock_ces_get_activity_response.json", ClientActivityResponse.class);
    when(cesClient.getTransferAndPayActivity()).thenReturn(cesResponse);

    TDSCoreTransferActivityResponse coreTransfersActivityResponse =
        createCoreTransfersActivityResponse();
    when(coreTransfersClient.getTransferActivity()).thenReturn(coreTransfersActivityResponse);

    // Verify activity retrieval includes the created transfer
    this.mockMvc
        .perform(
            get("/v2/activity")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header("Authorization", BEARER_TOKEN)
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.status").value("SUCCESS"))
        .andExpect(jsonPath("$.recentActivities").isArray())
        .andExpect(jsonPath("$.upcomingActivities").isArray())
        .andExpect(jsonPath("$.recentActivities[?(@.amount == 100.50)]").exists())
        .andExpect(jsonPath("$.recentActivities[?(@.displayStatus == 'Completed')]").exists());
  }

  @Test
  @DisplayName("Multiple transfer creation and activity verification")
  void createMultipleTransfersAndVerifyActivities() throws Exception {
    // Setup feature flags
    when(featureFlagService.isFeatureEnabled(ORCHESTRATOR_ENABLED)).thenReturn(false);
    when(featureFlagService.isFeatureEnabled(CORE_TRANSFERS_TDS_ENABLED)).thenReturn(true);

    // Mock successful core transfer creation
    TDSCoreTransferResponse transferResponse1 = createSuccessfulTransferResponse();
    transferResponse1.setReferenceId("REF-001");
    TDSCoreTransferResponse transferResponse2 = createSuccessfulTransferResponse();
    transferResponse2.setReferenceId("REF-002");

    when(coreTransfersClient.tdsCoreTransfer(any()))
        .thenReturn(transferResponse1)
        .thenReturn(transferResponse2);

    // Create first transfer
    var transferContent1 = createTransferRequest("DDA", "SAV", new BigDecimal("100.00"));
    this.mockMvc
        .perform(
            post("/activity")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header("Authorization", BEARER_TOKEN)
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .content(transferContent1)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.status").value("SUCCESS"));

    // Create second transfer
    var transferContent2 = createTransferRequest("SAV", "DDA", new BigDecimal("50.00"));
    this.mockMvc
        .perform(
            post("/activity")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header("Authorization", BEARER_TOKEN)
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .content(transferContent2)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.status").value("SUCCESS"));

    // Verify both transfers were created
    verify(coreTransfersClient, times(2)).tdsCoreTransfer(any());
  }

  // Helper methods for creating test data
  private String createImmediateTransferRequest() {
    return createTransferRequest("DDA", "SAV", new BigDecimal("100.50"));
  }

  private String createTransferRequest(
      String fromAccountType, String toAccountType, BigDecimal amount) {
    return
        """
        {
            "amount": %s,
            "dueDate": "%s",
            "frequency": "ONE_TIME",
            "fromAccountId": "abf3228b-5066-4729-8e15-b7d44afc0a8a",
            "toAccountId": "8a793e2a-21cc-4d50-af8a-76cab53935a2",
            "memo": "Test immediate transfer",
            "requestGuid": "267512b1-c64e-4219-ba36-1be9b6a28811",
            "scheduleImmediately": true,
            "activityType": "INTERNAL_TRANSFER",
            "fromAccountType": "%s",
            "toAccountType": "%s"
        }
        """
        .formatted(amount, LocalDate.now(), fromAccountType, toAccountType);
  }

  private TDSCoreTransferResponse createSuccessfulTransferResponse() {
    TDSCoreTransferResponse response = new TDSCoreTransferResponse();
    response.setReferenceId("REF-12345");
    return response;
  }

  private TDSCoreTransferResponse createErrorTransferResponse(
      String errorCode, String errorMessage) {
    TDSCoreTransferResponse response = new TDSCoreTransferResponse();
    TDSCoreTransferResponse.ErrorResponse error = new TDSCoreTransferResponse.ErrorResponse();
    error.setCode(errorCode);
    error.setMessage(errorMessage);
    error.setTarget("fromAccountId");
    response.setError(error);
    return response;
  }

  private TDSCoreTransferActivityResponse createCoreTransfersActivityResponse() {
    TDSCoreTransferActivity activity = new TDSCoreTransferActivity();
    activity.setReferenceId("REF-12345");
    activity.setFromAccountId("abf3228b-5066-4729-8e15-b7d44afc0a8a");
    activity.setFromAccountType("DDA");
    activity.setToAccountId("8a793e2a-21cc-4d50-af8a-76cab53935a2");
    activity.setToAccountType("SAV");
    activity.setAmount(new BigDecimal("100.50"));
    activity.setTransferStatus("SUCCESS");
    activity.setCreatedDate(LocalDate.now());
    activity.setExpectedPostingDate(LocalDate.now());

    TDSCoreTransferActivityResponse response = new TDSCoreTransferActivityResponse();
    response.setTransferActivities(Arrays.asList(activity));
    return response;
  }

  private TDSCoreTransferActivityResponse createMultipleCoreTransfersActivities() {
    // Create completed activity
    TDSCoreTransferActivity completedActivity = new TDSCoreTransferActivity();
    completedActivity.setReferenceId("REF-COMPLETED-1");
    completedActivity.setFromAccountId("account-1");
    completedActivity.setToAccountId("account-2");
    completedActivity.setAmount(new BigDecimal("50.00"));
    completedActivity.setTransferStatus("SUCCESS");
    completedActivity.setCreatedDate(LocalDate.now().minusDays(1));
    completedActivity.setExpectedPostingDate(LocalDate.now().minusDays(1));

    // Create pending activity
    TDSCoreTransferActivity pendingActivity = new TDSCoreTransferActivity();
    pendingActivity.setReferenceId("REF-PENDING-1");
    pendingActivity.setFromAccountId("account-3");
    pendingActivity.setToAccountId("account-4");
    pendingActivity.setAmount(new BigDecimal("75.00"));
    pendingActivity.setTransferStatus("PENDING");
    pendingActivity.setCreatedDate(LocalDate.now());
    pendingActivity.setExpectedPostingDate(LocalDate.now().plusDays(1));

    // Create another completed activity
    TDSCoreTransferActivity anotherCompletedActivity = new TDSCoreTransferActivity();
    anotherCompletedActivity.setReferenceId("REF-COMPLETED-2");
    anotherCompletedActivity.setFromAccountId("account-5");
    anotherCompletedActivity.setToAccountId("account-6");
    anotherCompletedActivity.setAmount(new BigDecimal("125.00"));
    anotherCompletedActivity.setTransferStatus("SUCCESS");
    anotherCompletedActivity.setCreatedDate(LocalDate.now().minusDays(2));
    anotherCompletedActivity.setExpectedPostingDate(LocalDate.now().minusDays(2));

    TDSCoreTransferActivityResponse response = new TDSCoreTransferActivityResponse();
    response.setTransferActivities(
        Arrays.asList(completedActivity, pendingActivity, anotherCompletedActivity));
    return response;
  }

  private TDSCoreTransferActivityResponse createActivitiesWithDifferentStatuses() {
    // Create successful/completed activity
    TDSCoreTransferActivity successActivity = new TDSCoreTransferActivity();
    successActivity.setReferenceId("REF-SUCCESS");
    successActivity.setFromAccountId("account-1");
    successActivity.setToAccountId("account-2");
    successActivity.setAmount(new BigDecimal("100.00"));
    successActivity.setTransferStatus("SUCCESS");
    successActivity.setCreatedDate(LocalDate.now().minusDays(1));
    successActivity.setExpectedPostingDate(LocalDate.now().minusDays(1));

    // Create failed activity
    TDSCoreTransferActivity failedActivity = new TDSCoreTransferActivity();
    failedActivity.setReferenceId("REF-FAILED");
    failedActivity.setFromAccountId("account-3");
    failedActivity.setToAccountId("account-4");
    failedActivity.setAmount(new BigDecimal("200.00"));
    failedActivity.setTransferStatus("INVALID_ACC_SOURCE");
    failedActivity.setCreatedDate(LocalDate.now().minusDays(2));
    failedActivity.setExpectedPostingDate(LocalDate.now().minusDays(2));

    // Create pending/scheduled activity
    TDSCoreTransferActivity pendingActivity = new TDSCoreTransferActivity();
    pendingActivity.setReferenceId("REF-PENDING");
    pendingActivity.setFromAccountId("account-5");
    pendingActivity.setToAccountId("account-6");
    pendingActivity.setAmount(new BigDecimal("150.00"));
    pendingActivity.setTransferStatus("PENDING");
    pendingActivity.setCreatedDate(LocalDate.now());
    pendingActivity.setExpectedPostingDate(LocalDate.now().plusDays(1));

    // Create validation failure activity
    TDSCoreTransferActivity validationFailureActivity = new TDSCoreTransferActivity();
    validationFailureActivity.setReferenceId("REF-VALIDATION-FAILURE");
    validationFailureActivity.setFromAccountId("account-7");
    validationFailureActivity.setToAccountId("account-8");
    validationFailureActivity.setAmount(new BigDecimal("300.00"));
    validationFailureActivity.setTransferStatus("VALIDATION_FAILURE");
    validationFailureActivity.setCreatedDate(LocalDate.now().minusDays(3));
    validationFailureActivity.setExpectedPostingDate(LocalDate.now().minusDays(3));

    TDSCoreTransferActivityResponse response = new TDSCoreTransferActivityResponse();
    response.setTransferActivities(
        Arrays.asList(successActivity, failedActivity, pendingActivity, validationFailureActivity));
    return response;
  }
}
